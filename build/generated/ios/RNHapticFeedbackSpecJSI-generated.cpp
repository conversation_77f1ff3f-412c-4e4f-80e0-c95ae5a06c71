/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "RNHapticFeedbackSpecJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeHapticFeedbackCxxSpecJSI_trigger(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeHapticFeedbackCxxSpecJSI *>(&turboModule)->trigger(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asString(rt),
    count <= 1 || args[1].isUndefined() ? std::nullopt : std::make_optional(args[1].asObject(rt))
  );
  return jsi::Value::undefined();
}

NativeHapticFeedbackCxxSpecJSI::NativeHapticFeedbackCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("RNHapticFeedback", jsInvoker) {
  methodMap_["trigger"] = MethodMetadata {2, __hostFunction_NativeHapticFeedbackCxxSpecJSI_trigger};
}


} // namespace facebook::react
