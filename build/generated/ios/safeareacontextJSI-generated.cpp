/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "safeareacontextJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeSafeAreaContextCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeSafeAreaContextCxxSpecJSI *>(&turboModule)->getConstants(
    rt
  );
}

NativeSafeAreaContextCxxSpecJSI::NativeSafeAreaContextCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("RNCSafeAreaContext", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeSafeAreaContextCxxSpecJSI_getConstants};
}


} // namespace facebook::react
