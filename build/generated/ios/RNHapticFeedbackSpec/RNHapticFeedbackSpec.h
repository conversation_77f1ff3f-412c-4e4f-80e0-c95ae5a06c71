/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#ifndef __cplusplus
#error This file must be compiled as Obj-C++. If you are importing it, you must change your file extension to .mm.
#endif

// Avoid multiple includes of RNHapticFeedbackSpec symbols
#ifndef RNHapticFeedbackSpec_H
#define RNHapticFeedbackSpec_H

#import <Foundation/Foundation.h>
#import <RCTRequired/RCTRequired.h>
#import <RCTTypeSafety/RCTConvertHelpers.h>
#import <RCTTypeSafety/RCTTypedModuleConstants.h>
#import <React/RCTBridgeModule.h>
#import <React/RCTCxxConvert.h>
#import <React/RCTManagedPointer.h>
#import <ReactCommon/RCTTurboModule.h>
#import <optional>
#import <vector>

namespace JS {
  namespace NativeHapticFeedback {
    struct SpecTriggerOptions {
      std::optional<bool> enableVibrateFallback() const;
      std::optional<bool> ignoreAndroidSystemSettings() const;

      SpecTriggerOptions(NSDictionary *const v) : _v(v) {}
    private:
      NSDictionary *_v;
    };
  }
}

@interface RCTCxxConvert (NativeHapticFeedback_SpecTriggerOptions)
+ (RCTManagedPointer *)JS_NativeHapticFeedback_SpecTriggerOptions:(id)json;
@end
@protocol NativeHapticFeedbackSpec <RCTBridgeModule, RCTTurboModule>

- (void)trigger:(NSString *)type
        options:(JS::NativeHapticFeedback::SpecTriggerOptions &)options;

@end

@interface NativeHapticFeedbackSpecBase : NSObject {
@protected
facebook::react::EventEmitterCallback _eventEmitterCallback;
}
- (void)setEventEmitterCallback:(EventEmitterCallbackWrapper *)eventEmitterCallbackWrapper;


@end

namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeHapticFeedback'
   */
  class JSI_EXPORT NativeHapticFeedbackSpecJSI : public ObjCTurboModule {
  public:
    NativeHapticFeedbackSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
inline std::optional<bool> JS::NativeHapticFeedback::SpecTriggerOptions::enableVibrateFallback() const
{
  id const p = _v[@"enableVibrateFallback"];
  return RCTBridgingToOptionalBool(p);
}
inline std::optional<bool> JS::NativeHapticFeedback::SpecTriggerOptions::ignoreAndroidSystemSettings() const
{
  id const p = _v[@"ignoreAndroidSystemSettings"];
  return RCTBridgingToOptionalBool(p);
}
#endif // RNHapticFeedbackSpec_H
