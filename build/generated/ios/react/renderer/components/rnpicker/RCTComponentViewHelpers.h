/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GenerateComponentHObjCpp.js
*/

#import <Foundation/Foundation.h>
#import <React/RCTDefines.h>
#import <React/RCTLog.h>

NS_ASSUME_NONNULL_BEGIN

@protocol RCTRNCPickerViewProtocol <NSObject>
- (void)setNativeSelectedIndex:(NSInteger)selectedIndex;
@end

RCT_EXTERN inline void RCTRNCPickerHandleCommand(
  id<RCTRNCPickerViewProtocol> componentView,
  NSString const *commandName,
  NSArray const *args)
{
  if ([commandName isEqualToString:@"setNativeSelectedIndex"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNCPicker", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"number", @"RNCPicker", commandName, @"1st")) {
    return;
  }
#endif
  NSInteger selectedIndex = [(NSNumber *)arg0 intValue];

  [componentView setNativeSelectedIndex:selectedIndex];
  return;
}

#if RCT_DEBUG
  RCTLogError(@"%@ received command %@, which is not a supported command.", @"RNCPicker", commandName);
#endif
}

NS_ASSUME_NONNULL_END