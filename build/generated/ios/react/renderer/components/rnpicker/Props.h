
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsH.js
 */
#pragma once

#include <folly/dynamic.h>
#include <react/renderer/components/view/ViewProps.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/core/propsConversions.h>
#include <react/renderer/graphics/Color.h>
#include <vector>

namespace facebook::react {

struct RNCAndroidDialogPickerItemsStyleStruct {
  SharedColor backgroundColor{};
  SharedColor color{};
  double fontSize{0.0};
  std::string fontFamily{};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCAndroidDialogPickerItemsStyleStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_backgroundColor = map.find("backgroundColor");
  if (tmp_backgroundColor != map.end()) {
    fromRawValue(context, tmp_backgroundColor->second, result.backgroundColor);
  }
  auto tmp_color = map.find("color");
  if (tmp_color != map.end()) {
    fromRawValue(context, tmp_color->second, result.color);
  }
  auto tmp_fontSize = map.find("fontSize");
  if (tmp_fontSize != map.end()) {
    fromRawValue(context, tmp_fontSize->second, result.fontSize);
  }
  auto tmp_fontFamily = map.find("fontFamily");
  if (tmp_fontFamily != map.end()) {
    fromRawValue(context, tmp_fontFamily->second, result.fontFamily);
  }
}

static inline std::string toString(const RNCAndroidDialogPickerItemsStyleStruct &value) {
  return "[Object RNCAndroidDialogPickerItemsStyleStruct]";
}

struct RNCAndroidDialogPickerItemsStruct {
  std::string label{};
  std::string value{};
  SharedColor color{};
  std::string fontFamily{};
  bool enabled{false};
  RNCAndroidDialogPickerItemsStyleStruct style{};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCAndroidDialogPickerItemsStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_label = map.find("label");
  if (tmp_label != map.end()) {
    fromRawValue(context, tmp_label->second, result.label);
  }
  auto tmp_value = map.find("value");
  if (tmp_value != map.end()) {
    fromRawValue(context, tmp_value->second, result.value);
  }
  auto tmp_color = map.find("color");
  if (tmp_color != map.end()) {
    fromRawValue(context, tmp_color->second, result.color);
  }
  auto tmp_fontFamily = map.find("fontFamily");
  if (tmp_fontFamily != map.end()) {
    fromRawValue(context, tmp_fontFamily->second, result.fontFamily);
  }
  auto tmp_enabled = map.find("enabled");
  if (tmp_enabled != map.end()) {
    fromRawValue(context, tmp_enabled->second, result.enabled);
  }
  auto tmp_style = map.find("style");
  if (tmp_style != map.end()) {
    fromRawValue(context, tmp_style->second, result.style);
  }
}

static inline std::string toString(const RNCAndroidDialogPickerItemsStruct &value) {
  return "[Object RNCAndroidDialogPickerItemsStruct]";
}

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, std::vector<RNCAndroidDialogPickerItemsStruct> &result) {
  auto items = (std::vector<RawValue>)value;
  for (const auto &item : items) {
    RNCAndroidDialogPickerItemsStruct newItem;
    fromRawValue(context, item, newItem);
    result.emplace_back(newItem);
  }
}

class RNCAndroidDialogPickerProps final : public ViewProps {
 public:
  RNCAndroidDialogPickerProps() = default;
  RNCAndroidDialogPickerProps(const PropsParserContext& context, const RNCAndroidDialogPickerProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::vector<RNCAndroidDialogPickerItemsStruct> items{};
  SharedColor color{};
  std::string prompt{};
  bool enabled{false};
  int selected{0};
  int backgroundColor{0};
  int dropdownIconColor{0};
  int dropdownIconRippleColor{0};
  int numberOfLines{0};
};

struct RNCAndroidDropdownPickerItemsStyleStruct {
  SharedColor backgroundColor{};
  SharedColor color{};
  double fontSize{0.0};
  std::string fontFamily{};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCAndroidDropdownPickerItemsStyleStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_backgroundColor = map.find("backgroundColor");
  if (tmp_backgroundColor != map.end()) {
    fromRawValue(context, tmp_backgroundColor->second, result.backgroundColor);
  }
  auto tmp_color = map.find("color");
  if (tmp_color != map.end()) {
    fromRawValue(context, tmp_color->second, result.color);
  }
  auto tmp_fontSize = map.find("fontSize");
  if (tmp_fontSize != map.end()) {
    fromRawValue(context, tmp_fontSize->second, result.fontSize);
  }
  auto tmp_fontFamily = map.find("fontFamily");
  if (tmp_fontFamily != map.end()) {
    fromRawValue(context, tmp_fontFamily->second, result.fontFamily);
  }
}

static inline std::string toString(const RNCAndroidDropdownPickerItemsStyleStruct &value) {
  return "[Object RNCAndroidDropdownPickerItemsStyleStruct]";
}

struct RNCAndroidDropdownPickerItemsStruct {
  std::string label{};
  std::string value{};
  SharedColor color{};
  std::string fontFamily{};
  bool enabled{false};
  RNCAndroidDropdownPickerItemsStyleStruct style{};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCAndroidDropdownPickerItemsStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_label = map.find("label");
  if (tmp_label != map.end()) {
    fromRawValue(context, tmp_label->second, result.label);
  }
  auto tmp_value = map.find("value");
  if (tmp_value != map.end()) {
    fromRawValue(context, tmp_value->second, result.value);
  }
  auto tmp_color = map.find("color");
  if (tmp_color != map.end()) {
    fromRawValue(context, tmp_color->second, result.color);
  }
  auto tmp_fontFamily = map.find("fontFamily");
  if (tmp_fontFamily != map.end()) {
    fromRawValue(context, tmp_fontFamily->second, result.fontFamily);
  }
  auto tmp_enabled = map.find("enabled");
  if (tmp_enabled != map.end()) {
    fromRawValue(context, tmp_enabled->second, result.enabled);
  }
  auto tmp_style = map.find("style");
  if (tmp_style != map.end()) {
    fromRawValue(context, tmp_style->second, result.style);
  }
}

static inline std::string toString(const RNCAndroidDropdownPickerItemsStruct &value) {
  return "[Object RNCAndroidDropdownPickerItemsStruct]";
}

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, std::vector<RNCAndroidDropdownPickerItemsStruct> &result) {
  auto items = (std::vector<RawValue>)value;
  for (const auto &item : items) {
    RNCAndroidDropdownPickerItemsStruct newItem;
    fromRawValue(context, item, newItem);
    result.emplace_back(newItem);
  }
}

class RNCAndroidDropdownPickerProps final : public ViewProps {
 public:
  RNCAndroidDropdownPickerProps() = default;
  RNCAndroidDropdownPickerProps(const PropsParserContext& context, const RNCAndroidDropdownPickerProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::vector<RNCAndroidDropdownPickerItemsStruct> items{};
  SharedColor color{};
  std::string prompt{};
  bool enabled{false};
  int selected{0};
  int backgroundColor{0};
  int dropdownIconColor{0};
  int dropdownIconRippleColor{0};
  int numberOfLines{0};
};

struct RNCPickerItemsStruct {
  folly::dynamic label{};
  folly::dynamic value{};
  SharedColor textColor{};
  std::string testID{};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCPickerItemsStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_label = map.find("label");
  if (tmp_label != map.end()) {
    fromRawValue(context, tmp_label->second, result.label);
  }
  auto tmp_value = map.find("value");
  if (tmp_value != map.end()) {
    fromRawValue(context, tmp_value->second, result.value);
  }
  auto tmp_textColor = map.find("textColor");
  if (tmp_textColor != map.end()) {
    fromRawValue(context, tmp_textColor->second, result.textColor);
  }
  auto tmp_testID = map.find("testID");
  if (tmp_testID != map.end()) {
    fromRawValue(context, tmp_testID->second, result.testID);
  }
}

static inline std::string toString(const RNCPickerItemsStruct &value) {
  return "[Object RNCPickerItemsStruct]";
}

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, std::vector<RNCPickerItemsStruct> &result) {
  auto items = (std::vector<RawValue>)value;
  for (const auto &item : items) {
    RNCPickerItemsStruct newItem;
    fromRawValue(context, item, newItem);
    result.emplace_back(newItem);
  }
}


struct RNCPickerFakePropStruct {
  folly::dynamic label{};
  folly::dynamic value{};
  SharedColor textColor{};
  std::string testID{};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCPickerFakePropStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_label = map.find("label");
  if (tmp_label != map.end()) {
    fromRawValue(context, tmp_label->second, result.label);
  }
  auto tmp_value = map.find("value");
  if (tmp_value != map.end()) {
    fromRawValue(context, tmp_value->second, result.value);
  }
  auto tmp_textColor = map.find("textColor");
  if (tmp_textColor != map.end()) {
    fromRawValue(context, tmp_textColor->second, result.textColor);
  }
  auto tmp_testID = map.find("testID");
  if (tmp_testID != map.end()) {
    fromRawValue(context, tmp_testID->second, result.testID);
  }
}

static inline std::string toString(const RNCPickerFakePropStruct &value) {
  return "[Object RNCPickerFakePropStruct]";
}
class RNCPickerProps final : public ViewProps {
 public:
  RNCPickerProps() = default;
  RNCPickerProps(const PropsParserContext& context, const RNCPickerProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  std::vector<RNCPickerItemsStruct> items{};
  int selectedIndex{0};
  SharedColor selectionColor{};
  SharedColor color{};
  std::string textAlign{};
  int numberOfLines{0};
  int fontSize{0};
  std::string fontWeight{};
  std::string fontStyle{};
  std::string fontFamily{};
  std::string testID{};
  std::string themeVariant{};
  RNCPickerFakePropStruct fakeProp{};
};

} // namespace facebook::react
