
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateShadowNodeH.js
 */

#pragma once

#include <react/renderer/components/rnpicker/EventEmitters.h>
#include <react/renderer/components/rnpicker/Props.h>
#include <react/renderer/components/rnpicker/States.h>
#include <react/renderer/components/view/ConcreteViewShadowNode.h>
#include <jsi/jsi.h>

namespace facebook::react {

JSI_EXPORT extern const char RNCPickerComponentName[];

/*
 * `ShadowNode` for <RNCPicker> component.
 */
using RNCPickerShadowNode = ConcreteViewShadowNode<
    RNCPickerComponentName,
    RNCPickerProps,
    RNCPickerEventEmitter,
    RNCPickerState>;

} // namespace facebook::react
