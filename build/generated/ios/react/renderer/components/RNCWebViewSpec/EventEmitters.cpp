
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateEventEmitterCpp.js
 */

#include <react/renderer/components/RNCWebViewSpec/EventEmitters.h>


namespace facebook::react {

void RNCWebViewEventEmitter::onContentSizeChange(OnContentSizeChange $event) const {
  dispatchEvent("contentSizeChange", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "url", $event.url);
$payload.setProperty(runtime, "loading", $event.loading);
$payload.setProperty(runtime, "title", $event.title);
$payload.setProperty(runtime, "canGoBack", $event.canGoBack);
$payload.setProperty(runtime, "canGoForward", $event.canGoForward);
$payload.setProperty(runtime, "lockIdentifier", $event.lockIdentifier);
    return $payload;
  });
}


void RNCWebViewEventEmitter::onRenderProcessGone(OnRenderProcessGone $event) const {
  dispatchEvent("renderProcessGone", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "didCrash", $event.didCrash);
    return $payload;
  });
}


void RNCWebViewEventEmitter::onContentProcessDidTerminate(OnContentProcessDidTerminate $event) const {
  dispatchEvent("contentProcessDidTerminate", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "url", $event.url);
$payload.setProperty(runtime, "loading", $event.loading);
$payload.setProperty(runtime, "title", $event.title);
$payload.setProperty(runtime, "canGoBack", $event.canGoBack);
$payload.setProperty(runtime, "canGoForward", $event.canGoForward);
$payload.setProperty(runtime, "lockIdentifier", $event.lockIdentifier);
    return $payload;
  });
}


void RNCWebViewEventEmitter::onCustomMenuSelection(OnCustomMenuSelection $event) const {
  dispatchEvent("customMenuSelection", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "label", $event.label);
$payload.setProperty(runtime, "key", $event.key);
$payload.setProperty(runtime, "selectedText", $event.selectedText);
    return $payload;
  });
}


void RNCWebViewEventEmitter::onFileDownload(OnFileDownload $event) const {
  dispatchEvent("fileDownload", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "downloadUrl", $event.downloadUrl);
    return $payload;
  });
}


void RNCWebViewEventEmitter::onLoadingError(OnLoadingError $event) const {
  dispatchEvent("loadingError", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "url", $event.url);
$payload.setProperty(runtime, "loading", $event.loading);
$payload.setProperty(runtime, "title", $event.title);
$payload.setProperty(runtime, "canGoBack", $event.canGoBack);
$payload.setProperty(runtime, "canGoForward", $event.canGoForward);
$payload.setProperty(runtime, "lockIdentifier", $event.lockIdentifier);
$payload.setProperty(runtime, "domain", $event.domain);
$payload.setProperty(runtime, "code", $event.code);
$payload.setProperty(runtime, "description", $event.description);
    return $payload;
  });
}


void RNCWebViewEventEmitter::onLoadingFinish(OnLoadingFinish $event) const {
  dispatchEvent("loadingFinish", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "url", $event.url);
$payload.setProperty(runtime, "loading", $event.loading);
$payload.setProperty(runtime, "title", $event.title);
$payload.setProperty(runtime, "canGoBack", $event.canGoBack);
$payload.setProperty(runtime, "canGoForward", $event.canGoForward);
$payload.setProperty(runtime, "lockIdentifier", $event.lockIdentifier);
$payload.setProperty(runtime, "navigationType", toString($event.navigationType));
$payload.setProperty(runtime, "mainDocumentURL", $event.mainDocumentURL);
    return $payload;
  });
}


void RNCWebViewEventEmitter::onLoadingProgress(OnLoadingProgress $event) const {
  dispatchEvent("loadingProgress", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "url", $event.url);
$payload.setProperty(runtime, "loading", $event.loading);
$payload.setProperty(runtime, "title", $event.title);
$payload.setProperty(runtime, "canGoBack", $event.canGoBack);
$payload.setProperty(runtime, "canGoForward", $event.canGoForward);
$payload.setProperty(runtime, "lockIdentifier", $event.lockIdentifier);
$payload.setProperty(runtime, "progress", $event.progress);
    return $payload;
  });
}


void RNCWebViewEventEmitter::onLoadingStart(OnLoadingStart $event) const {
  dispatchEvent("loadingStart", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "url", $event.url);
$payload.setProperty(runtime, "loading", $event.loading);
$payload.setProperty(runtime, "title", $event.title);
$payload.setProperty(runtime, "canGoBack", $event.canGoBack);
$payload.setProperty(runtime, "canGoForward", $event.canGoForward);
$payload.setProperty(runtime, "lockIdentifier", $event.lockIdentifier);
$payload.setProperty(runtime, "navigationType", toString($event.navigationType));
$payload.setProperty(runtime, "mainDocumentURL", $event.mainDocumentURL);
    return $payload;
  });
}


void RNCWebViewEventEmitter::onHttpError(OnHttpError $event) const {
  dispatchEvent("httpError", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "url", $event.url);
$payload.setProperty(runtime, "loading", $event.loading);
$payload.setProperty(runtime, "title", $event.title);
$payload.setProperty(runtime, "canGoBack", $event.canGoBack);
$payload.setProperty(runtime, "canGoForward", $event.canGoForward);
$payload.setProperty(runtime, "lockIdentifier", $event.lockIdentifier);
$payload.setProperty(runtime, "description", $event.description);
$payload.setProperty(runtime, "statusCode", $event.statusCode);
    return $payload;
  });
}


void RNCWebViewEventEmitter::onMessage(OnMessage $event) const {
  dispatchEvent("message", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "url", $event.url);
$payload.setProperty(runtime, "loading", $event.loading);
$payload.setProperty(runtime, "title", $event.title);
$payload.setProperty(runtime, "canGoBack", $event.canGoBack);
$payload.setProperty(runtime, "canGoForward", $event.canGoForward);
$payload.setProperty(runtime, "lockIdentifier", $event.lockIdentifier);
$payload.setProperty(runtime, "data", $event.data);
    return $payload;
  });
}


void RNCWebViewEventEmitter::onOpenWindow(OnOpenWindow $event) const {
  dispatchEvent("openWindow", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "targetUrl", $event.targetUrl);
    return $payload;
  });
}


void RNCWebViewEventEmitter::onScroll(OnScroll $event) const {
  dispatchEvent("scroll", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    {
  auto contentInset = jsi::Object(runtime);
  contentInset.setProperty(runtime, "bottom", $event.contentInset.bottom);
  contentInset.setProperty(runtime, "left", $event.contentInset.left);
  contentInset.setProperty(runtime, "right", $event.contentInset.right);
  contentInset.setProperty(runtime, "top", $event.contentInset.top);
  $payload.setProperty(runtime, "contentInset", contentInset);
}
{
  auto contentOffset = jsi::Object(runtime);
  contentOffset.setProperty(runtime, "y", $event.contentOffset.y);
  contentOffset.setProperty(runtime, "x", $event.contentOffset.x);
  $payload.setProperty(runtime, "contentOffset", contentOffset);
}
{
  auto contentSize = jsi::Object(runtime);
  contentSize.setProperty(runtime, "height", $event.contentSize.height);
  contentSize.setProperty(runtime, "width", $event.contentSize.width);
  $payload.setProperty(runtime, "contentSize", contentSize);
}
{
  auto layoutMeasurement = jsi::Object(runtime);
  layoutMeasurement.setProperty(runtime, "height", $event.layoutMeasurement.height);
  layoutMeasurement.setProperty(runtime, "width", $event.layoutMeasurement.width);
  $payload.setProperty(runtime, "layoutMeasurement", layoutMeasurement);
}
{
  auto targetContentOffset = jsi::Object(runtime);
  targetContentOffset.setProperty(runtime, "y", $event.targetContentOffset.y);
  targetContentOffset.setProperty(runtime, "x", $event.targetContentOffset.x);
  $payload.setProperty(runtime, "targetContentOffset", targetContentOffset);
}
{
  auto velocity = jsi::Object(runtime);
  velocity.setProperty(runtime, "y", $event.velocity.y);
  velocity.setProperty(runtime, "x", $event.velocity.x);
  $payload.setProperty(runtime, "velocity", velocity);
}
$payload.setProperty(runtime, "zoomScale", $event.zoomScale);
$payload.setProperty(runtime, "responderIgnoreScroll", $event.responderIgnoreScroll);
    return $payload;
  });
}


void RNCWebViewEventEmitter::onShouldStartLoadWithRequest(OnShouldStartLoadWithRequest $event) const {
  dispatchEvent("shouldStartLoadWithRequest", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "url", $event.url);
$payload.setProperty(runtime, "loading", $event.loading);
$payload.setProperty(runtime, "title", $event.title);
$payload.setProperty(runtime, "canGoBack", $event.canGoBack);
$payload.setProperty(runtime, "canGoForward", $event.canGoForward);
$payload.setProperty(runtime, "lockIdentifier", $event.lockIdentifier);
$payload.setProperty(runtime, "navigationType", toString($event.navigationType));
$payload.setProperty(runtime, "mainDocumentURL", $event.mainDocumentURL);
$payload.setProperty(runtime, "isTopFrame", $event.isTopFrame);
    return $payload;
  });
}

} // namespace facebook::react
