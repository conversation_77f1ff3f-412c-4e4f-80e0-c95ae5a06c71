/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GenerateComponentHObjCpp.js
*/

#import <Foundation/Foundation.h>
#import <React/RCTDefines.h>
#import <React/RCTLog.h>

NS_ASSUME_NONNULL_BEGIN

@protocol RCTRNCWebViewViewProtocol <NSObject>
- (void)goBack;
- (void)goForward;
- (void)reload;
- (void)stopLoading;
- (void)injectJavaScript:(NSString *)javascript;
- (void)requestFocus;
- (void)postMessage:(NSString *)data;
- (void)loadUrl:(NSString *)url;
- (void)clearFormData;
- (void)clearCache:(BOOL)includeDiskFiles;
- (void)clearHistory;
@end

RCT_EXTERN inline void RCTRNCWebViewHandleCommand(
  id<RCTRNCWebViewViewProtocol> componentView,
  NSString const *commandName,
  NSArray const *args)
{
  if ([commandName isEqualToString:@"goBack"]) {
#if RCT_DEBUG
  if ([args count] != 0) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNCWebView", commandName, (int)[args count], 0);
    return;
  }
#endif

  

  [componentView goBack];
  return;
}

if ([commandName isEqualToString:@"goForward"]) {
#if RCT_DEBUG
  if ([args count] != 0) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNCWebView", commandName, (int)[args count], 0);
    return;
  }
#endif

  

  [componentView goForward];
  return;
}

if ([commandName isEqualToString:@"reload"]) {
#if RCT_DEBUG
  if ([args count] != 0) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNCWebView", commandName, (int)[args count], 0);
    return;
  }
#endif

  

  [componentView reload];
  return;
}

if ([commandName isEqualToString:@"stopLoading"]) {
#if RCT_DEBUG
  if ([args count] != 0) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNCWebView", commandName, (int)[args count], 0);
    return;
  }
#endif

  

  [componentView stopLoading];
  return;
}

if ([commandName isEqualToString:@"injectJavaScript"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNCWebView", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSString class], @"string", @"RNCWebView", commandName, @"1st")) {
    return;
  }
#endif
  NSString * javascript = (NSString *)arg0;

  [componentView injectJavaScript:javascript];
  return;
}

if ([commandName isEqualToString:@"requestFocus"]) {
#if RCT_DEBUG
  if ([args count] != 0) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNCWebView", commandName, (int)[args count], 0);
    return;
  }
#endif

  

  [componentView requestFocus];
  return;
}

if ([commandName isEqualToString:@"postMessage"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNCWebView", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSString class], @"string", @"RNCWebView", commandName, @"1st")) {
    return;
  }
#endif
  NSString * data = (NSString *)arg0;

  [componentView postMessage:data];
  return;
}

if ([commandName isEqualToString:@"loadUrl"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNCWebView", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSString class], @"string", @"RNCWebView", commandName, @"1st")) {
    return;
  }
#endif
  NSString * url = (NSString *)arg0;

  [componentView loadUrl:url];
  return;
}

if ([commandName isEqualToString:@"clearFormData"]) {
#if RCT_DEBUG
  if ([args count] != 0) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNCWebView", commandName, (int)[args count], 0);
    return;
  }
#endif

  

  [componentView clearFormData];
  return;
}

if ([commandName isEqualToString:@"clearCache"]) {
#if RCT_DEBUG
  if ([args count] != 1) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNCWebView", commandName, (int)[args count], 1);
    return;
  }
#endif

  NSObject *arg0 = args[0];
#if RCT_DEBUG
  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"RNCWebView", commandName, @"1st")) {
    return;
  }
#endif
  BOOL includeDiskFiles = [(NSNumber *)arg0 boolValue];

  [componentView clearCache:includeDiskFiles];
  return;
}

if ([commandName isEqualToString:@"clearHistory"]) {
#if RCT_DEBUG
  if ([args count] != 0) {
    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"RNCWebView", commandName, (int)[args count], 0);
    return;
  }
#endif

  

  [componentView clearHistory];
  return;
}

#if RCT_DEBUG
  RCTLogError(@"%@ received command %@, which is not a supported command.", @"RNCWebView", commandName);
#endif
}

NS_ASSUME_NONNULL_END