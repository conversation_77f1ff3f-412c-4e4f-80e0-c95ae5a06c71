
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateShadowNodeH.js
 */

#pragma once

#include <react/renderer/components/RNCWebViewSpec/EventEmitters.h>
#include <react/renderer/components/RNCWebViewSpec/Props.h>
#include <react/renderer/components/RNCWebViewSpec/States.h>
#include <react/renderer/components/view/ConcreteViewShadowNode.h>
#include <jsi/jsi.h>

namespace facebook::react {

JSI_EXPORT extern const char RNCWebViewComponentName[];

/*
 * `ShadowNode` for <RNCWebView> component.
 */
using RNCWebViewShadowNode = ConcreteViewShadowNode<
    RNCWebViewComponentName,
    RNCWebViewProps,
    RNCWebViewEventEmitter,
    RNCWebViewState>;

} // namespace facebook::react
