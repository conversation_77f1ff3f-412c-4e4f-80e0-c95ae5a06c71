
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsCpp.js
 */

#include <react/renderer/components/pagerview/Props.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/core/propsConversions.h>

namespace facebook::react {

RNCViewPagerProps::RNCViewPagerProps(
    const PropsParserContext &context,
    const RNCViewPagerProps &sourceProps,
    const RawProps &rawProps): ViewProps(context, sourceProps, rawProps),

    scrollEnabled(convertRawProp(context, rawProps, "scrollEnabled", sourceProps.scrollEnabled, {true})),
    layoutDirection(convertRawProp(context, rawProps, "layoutDirection", sourceProps.layoutDirection, {RNCViewPagerLayoutDirection::Ltr})),
    initialPage(convertRawProp(context, rawProps, "initialPage", sourceProps.initialPage, {0})),
    orientation(convertRawProp(context, rawProps, "orientation", sourceProps.orientation, {RNCViewPagerOrientation::Horizontal})),
    offscreenPageLimit(convertRawProp(context, rawProps, "offscreenPageLimit", sourceProps.offscreenPageLimit, {0})),
    pageMargin(convertRawProp(context, rawProps, "pageMargin", sourceProps.pageMargin, {0})),
    overScrollMode(convertRawProp(context, rawProps, "overScrollMode", sourceProps.overScrollMode, {RNCViewPagerOverScrollMode::Auto})),
    overdrag(convertRawProp(context, rawProps, "overdrag", sourceProps.overdrag, {false})),
    keyboardDismissMode(convertRawProp(context, rawProps, "keyboardDismissMode", sourceProps.keyboardDismissMode, {RNCViewPagerKeyboardDismissMode::None}))
      {}

} // namespace facebook::react
