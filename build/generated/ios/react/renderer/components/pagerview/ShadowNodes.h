
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateShadowNodeH.js
 */

#pragma once

#include <react/renderer/components/pagerview/EventEmitters.h>
#include <react/renderer/components/pagerview/Props.h>
#include <react/renderer/components/pagerview/States.h>
#include <react/renderer/components/view/ConcreteViewShadowNode.h>
#include <jsi/jsi.h>

namespace facebook::react {

JSI_EXPORT extern const char RNCViewPagerComponentName[];

/*
 * `ShadowNode` for <RNCViewPager> component.
 */
using RNCViewPagerShadowNode = ConcreteViewShadowNode<
    RNCViewPagerComponentName,
    RNCViewPagerProps,
    RNCViewPagerEventEmitter,
    RNCViewPagerState>;

} // namespace facebook::react
