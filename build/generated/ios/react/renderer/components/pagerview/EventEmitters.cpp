
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateEventEmitterCpp.js
 */

#include <react/renderer/components/pagerview/EventEmitters.h>


namespace facebook::react {

void RNCViewPagerEventEmitter::onPageScroll(OnPageScroll $event) const {
  dispatchEvent("pageScroll", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "position", $event.position);
$payload.setProperty(runtime, "offset", $event.offset);
    return $payload;
  });
}


void RNCViewPagerEventEmitter::onPageSelected(OnPageSelected $event) const {
  dispatchEvent("pageSelected", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "position", $event.position);
    return $payload;
  });
}


void RNCViewPagerEventEmitter::onPageScrollStateChanged(OnPageScrollStateChanged $event) const {
  dispatchEvent("pageScrollStateChanged", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "pageScrollState", toString($event.pageScrollState));
    return $payload;
  });
}

} // namespace facebook::react
