
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsH.js
 */
#pragma once

#include <react/renderer/components/view/ViewProps.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/graphics/Color.h>

namespace facebook::react {

class RNGestureHandlerButtonProps final : public ViewProps {
 public:
  RNGestureHandlerButtonProps() = default;
  RNGestureHandlerButtonProps(const PropsParserContext& context, const RNGestureHandlerButtonProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  bool exclusive{true};
  bool foreground{false};
  bool borderless{false};
  bool enabled{true};
  SharedColor rippleColor{};
  int rippleRadius{0};
  bool touchSoundDisabled{false};
  Float borderWidth{0.0};
  SharedColor borderColor{};
  std::string borderStyle{"solid"};
};

class RNGestureHandlerRootViewProps final : public ViewProps {
 public:
  RNGestureHandlerRootViewProps() = default;
  RNGestureHandlerRootViewProps(const PropsParserContext& context, const RNGestureHandlerRootViewProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  
};

} // namespace facebook::react
