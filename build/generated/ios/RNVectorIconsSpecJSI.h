/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#pragma once

#include <ReactCommon/TurboModule.h>
#include <react/bridging/Bridging.h>

namespace facebook::react {


  class JSI_EXPORT NativeRNVectorIconsCxxSpecJSI : public TurboModule {
protected:
  NativeRNVectorIconsCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Value getImageForFont(jsi::Runtime &rt, jsi::String fontName, jsi::String glyph, double fontSize, double color) = 0;
  virtual jsi::String getImageForFontSync(jsi::Runtime &rt, jsi::String fontName, jsi::String glyph, double fontSize, double color) = 0;
  virtual jsi::Value loadFontWithFileName(jsi::Runtime &rt, jsi::String fontFileName, jsi::String extension) = 0;

};

template <typename T>
class JSI_EXPORT NativeRNVectorIconsCxxSpec : public TurboModule {
public:
  jsi::Value get(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.get(rt, propName);
  }

  static constexpr std::string_view kModuleName = "RNVectorIcons";

protected:
  NativeRNVectorIconsCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeRNVectorIconsCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeRNVectorIconsCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeRNVectorIconsCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Value getImageForFont(jsi::Runtime &rt, jsi::String fontName, jsi::String glyph, double fontSize, double color) override {
      static_assert(
          bridging::getParameterCount(&T::getImageForFont) == 5,
          "Expected getImageForFont(...) to have 5 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getImageForFont, jsInvoker_, instance_, std::move(fontName), std::move(glyph), std::move(fontSize), std::move(color));
    }
    jsi::String getImageForFontSync(jsi::Runtime &rt, jsi::String fontName, jsi::String glyph, double fontSize, double color) override {
      static_assert(
          bridging::getParameterCount(&T::getImageForFontSync) == 5,
          "Expected getImageForFontSync(...) to have 5 parameters");

      return bridging::callFromJs<jsi::String>(
          rt, &T::getImageForFontSync, jsInvoker_, instance_, std::move(fontName), std::move(glyph), std::move(fontSize), std::move(color));
    }
    jsi::Value loadFontWithFileName(jsi::Runtime &rt, jsi::String fontFileName, jsi::String extension) override {
      static_assert(
          bridging::getParameterCount(&T::loadFontWithFileName) == 3,
          "Expected loadFontWithFileName(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::loadFontWithFileName, jsInvoker_, instance_, std::move(fontFileName), std::move(extension));
    }

  private:
    friend class NativeRNVectorIconsCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};

} // namespace facebook::react
