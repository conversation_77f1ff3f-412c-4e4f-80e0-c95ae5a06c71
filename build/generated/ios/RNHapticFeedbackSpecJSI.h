/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#pragma once

#include <ReactCommon/TurboModule.h>
#include <react/bridging/Bridging.h>

namespace facebook::react {


  class JSI_EXPORT NativeHapticFeedbackCxxSpecJSI : public TurboModule {
protected:
  NativeHapticFeedbackCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void trigger(jsi::Runtime &rt, jsi::String type, std::optional<jsi::Object> options) = 0;

};

template <typename T>
class JSI_EXPORT NativeHapticFeedbackCxxSpec : public TurboModule {
public:
  jsi::Value get(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.get(rt, propName);
  }

  static constexpr std::string_view kModuleName = "RNHapticFeedback";

protected:
  NativeHapticFeedbackCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeHapticFeedbackCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeHapticFeedbackCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeHapticFeedbackCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void trigger(jsi::Runtime &rt, jsi::String type, std::optional<jsi::Object> options) override {
      static_assert(
          bridging::getParameterCount(&T::trigger) == 3,
          "Expected trigger(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::trigger, jsInvoker_, instance_, std::move(type), std::move(options));
    }

  private:
    friend class NativeHapticFeedbackCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};

} // namespace facebook::react
