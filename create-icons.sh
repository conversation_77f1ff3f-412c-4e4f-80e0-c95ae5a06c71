#!/bin/bash

# Create simple launcher icons using ImageMagick (if available) or copy from React Native template
# For now, let's create simple colored squares as placeholders

# Create a simple icon using base64 encoded PNG data
create_icon() {
    local size=$1
    local dir=$2
    
    # Simple 48x48 green square PNG in base64
    local icon_data="iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="
    
    # Create a simple colored square
    echo "$icon_data" | base64 -d > "$dir/ic_launcher.png"
    echo "$icon_data" | base64 -d > "$dir/ic_launcher_round.png"
}

# Create icons for different densities
create_icon 48 "android/app/src/main/res/mipmap-mdpi"
create_icon 72 "android/app/src/main/res/mipmap-hdpi"
create_icon 96 "android/app/src/main/res/mipmap-xhdpi"
create_icon 144 "android/app/src/main/res/mipmap-xxhdpi"
create_icon 192 "android/app/src/main/res/mipmap-xxxhdpi"

echo "Created basic launcher icons"
