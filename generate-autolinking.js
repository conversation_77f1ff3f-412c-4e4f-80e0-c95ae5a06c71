#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Create the autolinking configuration that <PERSON><PERSON><PERSON> expects
const autolinkingConfig = {
  "project": {
    "android": {
      "sourceDir": "./android",
      "appName": "app", 
      "packageName": "com.ordrz.khanbaba",
      "applicationId": "com.ordrz.khanbaba",
      "mainActivity": ".MainActivity"
    }
  }
};

// Ensure the directory exists
const autolinkingDir = path.join(__dirname, 'android', 'build', 'generated', 'autolinking');
if (!fs.existsSync(autolinkingDir)) {
  fs.mkdirSync(autolinkingDir, { recursive: true });
}

// Write the autolinking.json file
const autolinkingPath = path.join(autolinkingDir, 'autolinking.json');
fs.writeFileSync(autolinkingPath, JSON.stringify(autolinkingConfig, null, 2));

console.log('Generated autolinking configuration at:', autolinkingPath);
