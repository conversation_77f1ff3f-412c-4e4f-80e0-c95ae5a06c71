module.exports = {
  dependencies: {
    'react-native-vector-icons': {
      platforms: {
        android: {
          sourceDir: '../node_modules/react-native-vector-icons/android',
          packageImportPath:
            'import com.oblador.vectoricons.VectorIconsPackage;',
        },
      },
    },
  },
  project: {
    android: {
      sourceDir: './android',
      appName: 'app',
      packageName: 'com.ordrz.khanbaba',
      applicationId: 'com.ordrz.khanbaba',
      mainActivity: '.MainActivity',
    },
    ios: {
      sourceDir: './ios',
      xcodeProject: {
        name: 'KhanBaba.xcworkspace',
        path: '.',
        isWorkspace: true,
      },
    },
  },
  assets: [
    './node_modules/react-native-vector-icons/Fonts/',
    './src/assets/fonts/',
  ],
};
