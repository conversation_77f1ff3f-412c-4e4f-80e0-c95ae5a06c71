{"dependencies": {"@react-native-async-storage/async-storage": {"root": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/@react-native-async-storage/async-storage", "name": "@react-native-async-storage/async-storage", "platforms": {"android": {"sourceDir": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/@react-native-async-storage/async-storage/android", "packageImportPath": "import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;", "packageInstance": "new AsyncStoragePackage()", "buildTypes": [], "libraryName": "rnasyncstorage", "componentDescriptors": [], "cmakeListsPath": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-gesture-handler": {"root": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-gesture-handler", "name": "react-native-gesture-handler", "platforms": {"android": {"sourceDir": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-gesture-handler/android", "packageImportPath": "import com.swmansion.gesturehandler.RNGestureHandlerPackage;", "packageInstance": "new RNGestureHandlerPackage()", "buildTypes": [], "libraryName": "rngesturehandler_codegen", "componentDescriptors": ["RNGestureHandlerButtonComponentDescriptor", "RNGestureHandlerRootViewComponentDescriptor"], "cmakeListsPath": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-haptic-feedback": {"root": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-haptic-feedback", "name": "react-native-haptic-feedback", "platforms": {"android": {"sourceDir": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-haptic-feedback/android", "packageImportPath": "import com.mkuczera.RNReactNativeHapticFeedbackPackage;", "packageInstance": "new RNReactNativeHapticFeedbackPackage()", "buildTypes": [], "libraryName": "RNHapticFeedbackSpec", "componentDescriptors": [], "cmakeListsPath": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-pager-view": {"root": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-pager-view", "name": "react-native-pager-view", "platforms": {"android": {"sourceDir": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-pager-view/android", "packageImportPath": "import com.reactnativepagerview.PagerViewPackage;", "packageInstance": "new PagerViewPackage()", "buildTypes": [], "libraryName": "pagerview", "componentDescriptors": ["RNCViewPagerComponentDescriptor"], "cmakeListsPath": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-reanimated": {"root": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-reanimated/android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-vector-icons": {"root": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-vector-icons", "name": "react-native-vector-icons", "platforms": {"android": {"sourceDir": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-vector-icons/android", "packageImportPath": "import com.oblador.vectoricons.VectorIconsPackage;", "packageInstance": "new VectorIconsPackage()", "buildTypes": [], "libraryName": "RNVectorIconsSpec", "componentDescriptors": [], "cmakeListsPath": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-webview": {"root": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-webview", "name": "react-native-webview", "platforms": {"android": {"sourceDir": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-webview/android", "packageImportPath": "import com.reactnativecommunity.webview.RNCWebViewPackage;", "packageInstance": "new RNCWebViewPackage()", "buildTypes": [], "libraryName": "RNCWebViewSpec", "componentDescriptors": ["RNCWebViewComponentDescriptor"], "cmakeListsPath": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}}}