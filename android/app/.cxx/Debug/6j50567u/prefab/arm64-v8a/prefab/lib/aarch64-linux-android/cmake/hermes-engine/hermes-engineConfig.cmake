if(NOT TARGET hermes-engine::libhermes)
add_library(hermes-engine::libhermes SHARED IMPORTED)
set_target_properties(hermes-engine::libhermes PROPERTIES
    IMPORTED_LOCATION "/Users/<USER>/.gradle/caches/transforms-4/c05988be4d70d6453d99b7c8cbd07de4/transformed/jetified-hermes-android-0.76.8-debug/prefab/modules/libhermes/libs/android.arm64-v8a/libhermes.so"
    INTERFACE_INCLUDE_DIRECTORIES "/Users/<USER>/.gradle/caches/transforms-4/c05988be4d70d6453d99b7c8cbd07de4/transformed/jetified-hermes-android-0.76.8-debug/prefab/modules/libhermes/include"
    INTERFACE_LINK_LIBRARIES ""
)
endif()

