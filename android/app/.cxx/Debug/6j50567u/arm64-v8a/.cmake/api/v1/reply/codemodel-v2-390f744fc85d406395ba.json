{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "appmodules", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-3140957486b42926ada6.json", "name": "appmodules", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a", "source": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}