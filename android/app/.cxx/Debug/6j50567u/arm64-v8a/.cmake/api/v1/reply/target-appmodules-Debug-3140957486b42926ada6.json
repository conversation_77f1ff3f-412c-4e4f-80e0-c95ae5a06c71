{"artifacts": [{"path": "/Volumes/Zafeer/react_apps/khan_baba/android/app/build/intermediates/cxx/Debug/6j50567u/obj/arm64-v8a/libappmodules.so"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "target_compile_options", "target_include_directories"], "files": ["/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 31, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 56, "parent": 2}, {"command": 2, "file": 0, "line": 87, "parent": 2}, {"command": 3, "file": 0, "line": 63, "parent": 2}, {"command": 4, "file": 0, "line": 58, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 5, "fragment": "-Wall"}, {"backtrace": 5, "fragment": "-Werror"}, {"backtrace": 5, "fragment": "-Wno-error=cpp"}, {"backtrace": 5, "fragment": "-fexceptions"}, {"backtrace": 5, "fragment": "-frtti"}, {"backtrace": 5, "fragment": "-std=c++20"}, {"backtrace": 5, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 5, "fragment": "-DFOLLY_NO_CONFIG=1"}], "defines": [{"define": "appmodules_EXPORTS"}], "includes": [{"backtrace": 6, "path": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, {"backtrace": 6, "path": "/Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni"}, {"backtrace": 4, "isSystem": true, "path": "/Users/<USER>/.gradle/caches/transforms-4/5fdc28e1e6a2515b2593720807553c01/transformed/jetified-fbjni-0.6.0/prefab/modules/fbjni/include"}, {"backtrace": 4, "isSystem": true, "path": "/Users/<USER>/.gradle/caches/transforms-4/7daf84a81f913fe36c764c4a51e8fbb9/transformed/jetified-react-android-0.76.8-debug/prefab/modules/jsi/include"}, {"backtrace": 4, "isSystem": true, "path": "/Users/<USER>/.gradle/caches/transforms-4/7daf84a81f913fe36c764c4a51e8fbb9/transformed/jetified-react-android-0.76.8-debug/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1], "sysroot": {"path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/sysroot"}}], "id": "appmodules::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 4, "fragment": "/Users/<USER>/.gradle/caches/transforms-4/5fdc28e1e6a2515b2593720807553c01/transformed/jetified-fbjni-0.6.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/Users/<USER>/.gradle/caches/transforms-4/7daf84a81f913fe36c764c4a51e8fbb9/transformed/jetified-react-android-0.76.8-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/Users/<USER>/.gradle/caches/transforms-4/7daf84a81f913fe36c764c4a51e8fbb9/transformed/jetified-react-android-0.76.8-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/sysroot"}}, "name": "appmodules", "nameOnDisk": "libappmodules.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "/Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "OnLoad.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}