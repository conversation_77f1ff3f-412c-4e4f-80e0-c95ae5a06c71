# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# input_SRC at /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/*.cpp")
set(OLD_GLOB
  "/Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at /Volumes/Zafeer/react_apps/khan_baba/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt:20 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Volumes/Zafeer/react_apps/khan_baba/node_modules/@react-native-picker/picker/android/src/main/jni/*.cpp")
set(OLD_GLOB
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerMeasurementsManager.cpp"
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerShadowNode.cpp"
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerState.cpp"
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerMeasurementsManager.cpp"
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerShadowNode.cpp"
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerState.cpp"
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/@react-native-picker/picker/android/src/main/jni/rnpicker.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at /Volumes/Zafeer/react_apps/khan_baba/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt:21 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Volumes/Zafeer/react_apps/khan_baba/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:21 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-safe-area-context/android/src/main/jni/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:21 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp"
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-screens/android/src/main/jni/*.cpp")
set(OLD_GLOB
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp"
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp"
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp"
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp"
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp"
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp"
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp"
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/utils/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:24 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# override_cpp_SRC at /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:42 (file)
# input_SRC at /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/*.cpp")
set(OLD_GLOB
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()
