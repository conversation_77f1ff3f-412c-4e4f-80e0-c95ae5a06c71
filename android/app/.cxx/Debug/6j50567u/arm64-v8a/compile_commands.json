[{"directory": "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dappmodules_EXPORTS -I/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni -isystem /Users/<USER>/.gradle/caches/transforms-4/5fdc28e1e6a2515b2593720807553c01/transformed/jetified-fbjni-0.6.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/transforms-4/7daf84a81f913fe36c764c4a51e8fbb9/transformed/jetified-react-android-0.76.8-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/transforms-4/7daf84a81f913fe36c764c4a51e8fbb9/transformed/jetified-react-android-0.76.8-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -o CMakeFiles/appmodules.dir/Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o -c /Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "file": "/Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"}, {"directory": "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dappmodules_EXPORTS -I/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni -isystem /Users/<USER>/.gradle/caches/transforms-4/5fdc28e1e6a2515b2593720807553c01/transformed/jetified-fbjni-0.6.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/transforms-4/7daf84a81f913fe36c764c4a51e8fbb9/transformed/jetified-react-android-0.76.8-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/transforms-4/7daf84a81f913fe36c764c4a51e8fbb9/transformed/jetified-react-android-0.76.8-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -o CMakeFiles/appmodules.dir/OnLoad.cpp.o -c /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp", "file": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"}]