                        -H/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125
-DC<PERSON><PERSON>_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Volumes/Zafeer/react_apps/khan_baba/android/app/build/intermediates/cxx/Debug/6j50567u/obj/arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Volumes/Zafeer/react_apps/khan_baba/android/app/build/intermediates/cxx/Debug/6j50567u/obj/arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/prefab/arm64-v8a/prefab
-B/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a
-GNinja
-DPROJECT_BUILD_DIR=/Volumes/Zafeer/react_apps/khan_baba/android/app/build
-DREACT_ANDROID_DIR=/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid
-DANDROID_STL=c++_shared
-DANDROID_USE_LEGACY_TOOLCHAIN_FILE=ON
                        Build command args: []
                        Version: 2