{"buildFiles": ["/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "armeabi-v7a", "output": "/Volumes/Zafeer/react_apps/khan_baba/android/app/build/intermediates/cxx/Debug/6j50567u/obj/armeabi-v7a/libappmodules.so", "runtimeFiles": ["/Users/<USER>/.gradle/caches/transforms-4/5fdc28e1e6a2515b2593720807553c01/transformed/jetified-fbjni-0.6.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so", "/Users/<USER>/.gradle/caches/transforms-4/7daf84a81f913fe36c764c4a51e8fbb9/transformed/jetified-react-android-0.76.8-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so", "/Users/<USER>/.gradle/caches/transforms-4/7daf84a81f913fe36c764c4a51e8fbb9/transformed/jetified-react-android-0.76.8-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so"]}}}