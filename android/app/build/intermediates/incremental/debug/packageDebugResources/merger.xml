<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res"><file path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/values/styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    </style><style name="Theme.App.SplashScreen" parent="AppTheme">
        <item name="android:windowBackground">@drawable/background_splash</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style></file><file path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/values/strings.xml" qualifiers=""><string name="app_name">Khan Baba</string></file><file name="background_splash" path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/drawable/background_splash.xml" qualifiers="" type="drawable"/><file name="rn_edit_text_material" path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/drawable/rn_edit_text_material.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/drawable/ic_launcher.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/drawable/ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/drawable/ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/mipmap-anydpi-v26/ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/mipmap-hdpi/ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/mipmap-hdpi/ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/mipmap-mdpi/ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/mipmap-mdpi/ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/mipmap-xhdpi/ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/mipmap-xhdpi/ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/mipmap-xxhdpi/ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Volumes/Zafeer/react_apps/khan_baba/android/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/res/resValues/debug"/><source path="/Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/res/processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/res/resValues/debug"><file path="/Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/res/resValues/debug/values/gradleResValues.xml" qualifiers=""><integer name="react_native_dev_server_port">8081</integer></file></source><source path="/Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/res/processDebugGoogleServices"><file path="/Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/res/processDebugGoogleServices/values/values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">1064042071410</string><string name="google_api_key" translatable="false">AIzaSyBVvm9R4XoD5zpOYo9L9FZG8wqsEtOk3h0</string><string name="google_app_id" translatable="false">1:1064042071410:android:13a7e780607afdfb19c9eb</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyBVvm9R4XoD5zpOYo9L9FZG8wqsEtOk3h0</string><string name="google_storage_bucket" translatable="false">khan-baba-45a5e.firebasestorage.app</string><string name="project_id" translatable="false">khan-baba-45a5e</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>