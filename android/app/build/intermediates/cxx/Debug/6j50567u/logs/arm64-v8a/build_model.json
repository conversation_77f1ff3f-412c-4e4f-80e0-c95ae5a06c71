{"info": {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, "cxxBuildFolder": "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a", "soFolder": "/Volumes/Zafeer/react_apps/khan_baba/android/app/build/intermediates/cxx/Debug/6j50567u/obj/arm64-v8a", "soRepublishFolder": "/Volumes/Zafeer/react_apps/khan_baba/android/app/build/intermediates/cmake/debug/obj/arm64-v8a", "abiPlatformVersion": 24, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DPROJECT_BUILD_DIR=/Volumes/Zafeer/react_apps/khan_baba/android/app/build", "-DREACT_ANDROID_DIR=/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid", "-DANDROID_STL=c++_shared", "-DANDROID_USE_LEGACY_TOOLCHAIN_FILE=ON"], "cFlagsList": [], "cppFlagsList": [], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx", "intermediatesBaseFolder": "/Volumes/Zafeer/react_apps/khan_baba/android/app/build/intermediates", "intermediatesFolder": "/Volumes/Zafeer/react_apps/khan_baba/android/app/build/intermediates/cxx", "gradleModulePathName": ":app", "moduleRootFolder": "/Volumes/Zafeer/react_apps/khan_baba/android/app", "moduleBuildFile": "/Volumes/Zafeer/react_apps/khan_baba/android/app/build.gradle", "makeFile": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125", "ndkFolderBeforeSymLinking": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125", "ndkVersion": "26.1.10909125", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 34, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake", "cmake": {"cmakeExe": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/arm-linux-androideabi/libc++_shared.so", "arm64-v8a": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/libc++_shared.so", "x86": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/i686-linux-android/libc++_shared.so", "x86_64": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/x86_64-linux-android/libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "/Volumes/Zafeer/react_apps/khan_baba/android", "sdkFolder": "/Users/<USER>/Library/Android/sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": true}, "outputOptions": [], "ninjaExe": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "hasBuildTimeInformation": true}, "prefabClassPaths": ["/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.prefab/cli/2.1.0/aa32fec809c44fa531f01dcfb739b5b3304d3050/cli-2.1.0-all.jar"], "prefabPackages": ["/Users/<USER>/.gradle/caches/transforms-4/7daf84a81f913fe36c764c4a51e8fbb9/transformed/jetified-react-android-0.76.8-debug/prefab", "/Users/<USER>/.gradle/caches/transforms-4/c05988be4d70d6453d99b7c8cbd07de4/transformed/jetified-hermes-android-0.76.8-debug/prefab", "/Users/<USER>/.gradle/caches/transforms-4/5fdc28e1e6a2515b2593720807553c01/transformed/jetified-fbjni-0.6.0/prefab"], "prefabPackageConfigurations": [], "stlType": "c++_shared", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/prefab/arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "6j50567u3a6a3t1v423wi6k391x5v1h31501n1nr654k1l5vbr3dy301o", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.6.1.\n#   - $NDK is the path to NDK 26.1.10909125.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-H/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=24\n-DANDROID_PLATFORM=android-24\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-DCMAKE_FIND_ROOT_PATH=$PROJECT/app/.cxx/Debug/$HASH/prefab/$ABI/prefab\n-B$PROJECT/app/.cxx/Debug/$HASH/$ABI\n-GNinja\n-DPROJECT_BUILD_DIR=$PROJECT/app/build\n-DREACT_ANDROID_DIR=/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid\n-DANDROID_STL=c++_shared\n-DANDROID_USE_LEGACY_TOOLCHAIN_FILE=ON", "configurationArguments": ["-H/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=24", "-DANDROID_PLATFORM=android-24", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125", "-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125", "-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Volumes/Zafeer/react_apps/khan_baba/android/app/build/intermediates/cxx/Debug/6j50567u/obj/arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Volumes/Zafeer/react_apps/khan_baba/android/app/build/intermediates/cxx/Debug/6j50567u/obj/arm64-v8a", "-DCMAKE_BUILD_TYPE=Debug", "-DCMAKE_FIND_ROOT_PATH=/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/prefab/arm64-v8a/prefab", "-B/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>", "-DPROJECT_BUILD_DIR=/Volumes/Zafeer/react_apps/khan_baba/android/app/build", "-DREACT_ANDROID_DIR=/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid", "-DANDROID_STL=c++_shared", "-DANDROID_USE_LEGACY_TOOLCHAIN_FILE=ON"], "stlLibraryFile": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/libc++_shared.so", "intermediatesParentFolder": "/Volumes/Zafeer/react_apps/khan_baba/android/app/build/intermediates/cxx/Debug/6j50567u"}