[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: arm64-v8a", "file_": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON /Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a/android_gradle_build.json due to:", "file_": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "/Users/<USER>/.sdkman/candidates/java/17.0.9-tem/bin/java \\\n  --class-path \\\n  /Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.prefab/cli/2.1.0/aa32fec809c44fa531f01dcfb739b5b3304d3050/cli-2.1.0-all.jar \\\n  com.google.prefab.cli.AppKt \\\n  --build-system \\\n  cmake \\\n  --platform \\\n  android \\\n  --abi \\\n  arm64-v8a \\\n  --os-version \\\n  24 \\\n  --stl \\\n  c++_shared \\\n  --ndk-version \\\n  26 \\\n  --output \\\n  /var/folders/7y/pyfrqhfx175c_nx3rshjslc80000gp/T/agp-prefab-staging7491040092724845884/staged-cli-output \\\n  /Users/<USER>/.gradle/caches/transforms-4/7daf84a81f913fe36c764c4a51e8fbb9/transformed/jetified-react-android-0.76.8-debug/prefab \\\n  /Users/<USER>/.gradle/caches/transforms-4/c05988be4d70d6453d99b7c8cbd07de4/transformed/jetified-hermes-android-0.76.8-debug/prefab \\\n  /Users/<USER>/.gradle/caches/transforms-4/5fdc28e1e6a2515b2593720807553c01/transformed/jetified-fbjni-0.6.0/prefab\n", "file_": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from '/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a'", "file_": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder '/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a'", "file_": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=24 \\\n  -DANDROID_PLATFORM=android-24 \\\n  -DANDROID_ABI=arm64-v8a \\\n  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125 \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Volumes/Zafeer/react_apps/khan_baba/android/app/build/intermediates/cxx/Debug/6j50567u/obj/arm64-v8a \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Volumes/Zafeer/react_apps/khan_baba/android/app/build/intermediates/cxx/Debug/6j50567u/obj/arm64-v8a \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -DCMAKE_FIND_ROOT_PATH=/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/prefab/arm64-v8a/prefab \\\n  -B/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a \\\n  -GNinja \\\n  -DPROJECT_BUILD_DIR=/Volumes/Zafeer/react_apps/khan_baba/android/app/build \\\n  -DREACT_ANDROID_DIR=/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid \\\n  -DANDROID_STL=c++_shared \\\n  -DANDROID_USE_LEGACY_TOOLCHAIN_FILE=ON\n", "file_": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=24 \\\n  -DANDROID_PLATFORM=android-24 \\\n  -DANDROID_ABI=arm64-v8a \\\n  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125 \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Volumes/Zafeer/react_apps/khan_baba/android/app/build/intermediates/cxx/Debug/6j50567u/obj/arm64-v8a \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Volumes/Zafeer/react_apps/khan_baba/android/app/build/intermediates/cxx/Debug/6j50567u/obj/arm64-v8a \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -DCMAKE_FIND_ROOT_PATH=/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/prefab/arm64-v8a/prefab \\\n  -B/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a \\\n  -GNinja \\\n  -DPROJECT_BUILD_DIR=/Volumes/Zafeer/react_apps/khan_baba/android/app/build \\\n  -DREACT_ANDROID_DIR=/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid \\\n  -DANDROID_STL=c++_shared \\\n  -DANDROID_USE_LEGACY_TOOLCHAIN_FILE=ON\n", "file_": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 1", "file_": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed with problem. Exception: com.android.ide.common.process.ProcessException: -- The C compiler identification is Clang 17.0.2\n-- The CXX compiler identification is Clang 17.0.2\n-- Detecting C compiler ABI info\n-- Detecting C compiler AB<PERSON> info - done\n-- Check for working C compiler: /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang - skipped\n-- Detecting C compile features\n-- Detecting C compile features - done\n-- Detecting CXX compiler ABI info\n-- Detecting CXX compiler ABI info - done\n-- Check for working CXX compiler: /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ - skipped\n-- Detecting CXX compile features\n-- Detecting CXX compile features - done\n-- Configuring incomplete, errors occurred!\nSee also \"/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a/CMakeFiles/CMakeOutput.log\".\n\nC++ build system [configure] failed while executing:\n    /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n      -H/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup \\\n      -DCMAKE_SYSTEM_NAME=Android \\\n      -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n      -DCMAKE_SYSTEM_VERSION=24 \\\n      -DANDROID_PLATFORM=android-24 \\\n      -DANDROID_ABI=arm64-v8a \\\n      -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \\\n      -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125 \\\n      -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125 \\\n      -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake \\\n      -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n      -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Volumes/Zafeer/react_apps/khan_baba/android/app/build/intermediates/cxx/Debug/6j50567u/obj/arm64-v8a \\\n      -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Volumes/Zafeer/react_apps/khan_baba/android/app/build/intermediates/cxx/Debug/6j50567u/obj/arm64-v8a \\\n      -DCMAKE_BUILD_TYPE=Debug \\\n      -DCMAKE_FIND_ROOT_PATH=/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/prefab/arm64-v8a/prefab \\\n      -B/Volumes/Zafeer/react_apps/khan_baba/android/app/.cxx/Debug/6j50567u/arm64-v8a \\\n      -GNinja \\\n      -DPROJECT_BUILD_DIR=/Volumes/Zafeer/react_apps/khan_baba/android/app/build \\\n      -DREACT_ANDROID_DIR=/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid \\\n      -DANDROID_STL=c++_shared \\\n      -DANDROID_USE_LEGACY_TOOLCHAIN_FILE=ON\n  from /Volumes/Zafeer/react_apps/khan_baba/android/app\nCMake Error at /Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake:9 (add_subdirectory):\n  add_subdirectory given source\n  \"/Volumes/Zafeer/react_apps/khan_baba/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/\"\n  which is not an existing directory.\nCall Stack (most recent call first):\n  /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:100 (include)\n  CMakeLists.txt:31 (include)\n\n\nCMake Error at /Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake:12 (add_subdirectory):\n  add_subdirectory given source\n  \"/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/\"\n  which is not an existing directory.\nCall Stack (most recent call first):\n  /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:100 (include)\n  CMakeLists.txt:31 (include)\n\n\nCMake Error at /Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake:13 (add_subdirectory):\n  add_subdirectory given source\n  \"/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/\"\n  which is not an existing directory.\nCall Stack (most recent call first):\n  /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:100 (include)\n  CMakeLists.txt:31 (include)\n\n\nCMake Error at /Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake:14 (add_subdirectory):\n  add_subdirectory given source\n  \"/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/\"\n  which is not an existing directory.\nCall Stack (most recent call first):\n  /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:100 (include)\n  CMakeLists.txt:31 (include)\n\n\nCMake Error at /Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake:16 (add_subdirectory):\n  add_subdirectory given source\n  \"/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/\"\n  which is not an existing directory.\nCall Stack (most recent call first):\n  /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:100 (include)\n  CMakeLists.txt:31 (include)\n\n\nCMake Error at /Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake:20 (add_subdirectory):\n  add_subdirectory given source\n  \"/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/\"\n  which is not an existing directory.\nCall Stack (most recent call first):\n  /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:100 (include)\n  CMakeLists.txt:31 (include)\n\n\nCMake Error at /Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake:21 (add_subdirectory):\n  add_subdirectory given source\n  \"/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-webview/android/build/generated/source/codegen/jni/\"\n  which is not an existing directory.\nCall Stack (most recent call first):\n  /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:100 (include)\n  CMakeLists.txt:31 (include)\n\n\nCMake Error at /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:103 (target_link_libraries):\n  Cannot specify link libraries for target \"react_codegen_rnasyncstorage\"\n  which is not built by this project.\nCall Stack (most recent call first):\n  CMakeLists.txt:31 (include)", "file_": "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]