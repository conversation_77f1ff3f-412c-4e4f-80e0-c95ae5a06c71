CMake Error at /Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake:9 (add_subdirectory):
  add_subdirectory given source
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/"
  which is not an existing directory.
Call Stack (most recent call first):
  /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:100 (include)
  CMakeLists.txt:31 (include)


CMake Error at /Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake:12 (add_subdirectory):
  add_subdirectory given source
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/"
  which is not an existing directory.
Call Stack (most recent call first):
  /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:100 (include)
  CMakeLists.txt:31 (include)


CMake Error at /Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake:13 (add_subdirectory):
  add_subdirectory given source
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/"
  which is not an existing directory.
Call Stack (most recent call first):
  /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:100 (include)
  CMakeLists.txt:31 (include)


CMake Error at /Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake:14 (add_subdirectory):
  add_subdirectory given source
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/"
  which is not an existing directory.
Call Stack (most recent call first):
  /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:100 (include)
  CMakeLists.txt:31 (include)


CMake Error at /Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake:16 (add_subdirectory):
  add_subdirectory given source
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/"
  which is not an existing directory.
Call Stack (most recent call first):
  /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:100 (include)
  CMakeLists.txt:31 (include)


CMake Error at /Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake:20 (add_subdirectory):
  add_subdirectory given source
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/"
  which is not an existing directory.
Call Stack (most recent call first):
  /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:100 (include)
  CMakeLists.txt:31 (include)


CMake Error at /Volumes/Zafeer/react_apps/khan_baba/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake:21 (add_subdirectory):
  add_subdirectory given source
  "/Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native-webview/android/build/generated/source/codegen/jni/"
  which is not an existing directory.
Call Stack (most recent call first):
  /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:100 (include)
  CMakeLists.txt:31 (include)


CMake Error at /Volumes/Zafeer/react_apps/khan_baba/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:103 (target_link_libraries):
  Cannot specify link libraries for target "react_codegen_rnasyncstorage"
  which is not built by this project.
Call Stack (most recent call first):
  CMakeLists.txt:31 (include)


